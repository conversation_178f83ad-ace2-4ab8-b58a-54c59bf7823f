[Dify]
enable = true
default-model = "学姐"
commands = [ "聊天", "AI", "重置对话" ]
support_agent_mode = true
command-tip = """
-----xxxBot-----
💬AI聊天指令：
1. 切换模型（将会一直保持到下次切换）：
   - @学姐 切换：切换到学姐模型
   - @老夏 切换：切换到老夏模型
2. 临时使用其他模型：
   - 学姐 消息内容：临时使用学姐模型
   - 老夏 消息内容：临时使用老夏模型
3. 重置对话：
   - 重置对话：清除当前对话历史，开始新的对话"""
admin_ignore = true
whitelist_ignore = true
http-proxy = ""
voice_reply_all = false
robot-names = [ "毛球", "🥥", "智能助手", "小小x" ]

[Dify.models."学姐"]
api-key = "app-zPoThZ1qVxei3DRVS7k4ZLqN"
base-url = "http://************:8080/v1"
trigger-words = [ "@学姐" ]
wakeup-words = [ "学姐" ]
price = 0
